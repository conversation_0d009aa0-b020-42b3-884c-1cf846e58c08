// إعداد محسن للاختبارات التكاملية
import dotenv from 'dotenv';
import { testPrisma, setupTestDatabase, cleanupTestDatabase, disconnectTestDatabase } from './test-database-setup.js';
import { clearMockDatabase } from '../__mocks__/@prisma/client.js';
import { generateToken } from '../jwt.js';
import bcrypt from 'bcryptjs';
import path from 'path';

// تعيين بيئة الاختبار أولاً
process.env.NODE_ENV = 'test';

// تعيين DATABASE_URL للاختبارات قبل تحميل أي شيء آخر
process.env.DATABASE_URL = 'file:./test.db';

// تحميل متغيرات البيئة للاختبار
dotenv.config({
  path: path.join(process.cwd(), '.env.test'),
  override: false
});

// تحميل ملف .env الافتراضي كـ fallback
dotenv.config({
  path: path.join(process.cwd(), '.env'),
  override: false
});

// إعدادات JWT للاختبار
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
}

if (!process.env.JWT_REFRESH_SECRET) {
  process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret_key_for_testing_only';
}

// تعطيل الـ logging في الاختبارات
process.env.LOG_LEVEL = 'error';

// التأكد من DATABASE_URL للاختبارات
process.env.DATABASE_URL = 'file:./test.db';

/**
 * إعداد قاعدة البيانات للاختبارات التكاملية
 */
export const setupIntegrationDatabase = async () => {
  try {
    console.log('📋 إعداد قاعدة البيانات للاختبارات...');

    // تعيين DATABASE_URL للاختبارات إذا لم يكن موجود
    if (!process.env.DATABASE_URL || process.env.DATABASE_URL.includes('postgresql')) {
      process.env.DATABASE_URL = 'file:./test.db';
    }

    // استخدام الدالة المحسنة من test-database-setup
    await setupTestDatabase();

    console.log('✅ تم إعداد قاعدة البيانات للاختبارات');
  } catch (error) {
    console.warn('⚠️ تحذير في إعداد قاعدة البيانات:', error instanceof Error ? error.message : String(error));
    // لا نرمي خطأ، سنستمر مع mock
  }
};

/**
 * تنظيف قاعدة البيانات
 */
export const cleanupIntegrationDatabase = async (preserveSharedUser = true): Promise<void> => {
  try {
    // تنظيف قاعدة البيانات الوهمية
    clearMockDatabase();

    // استخدام الدالة المحسنة من test-database-setup
    await cleanupTestDatabase(!preserveSharedUser);

    if (preserveSharedUser && sharedTestUser) {
      console.log('🧹 تم تنظيف قاعدة البيانات مع الحفاظ على المستخدم المشترك');
    } else {
      sharedTestUser = null; // إعادة تعيين المستخدم المشترك
      console.log('🧹 تم تنظيف قاعدة البيانات بالكامل');
    }
  } catch (error) {
    console.warn('تحذير: خطأ عام في تنظيف قاعدة البيانات:', error);
  }
};

// متغير لحفظ المستخدم المشترك
let sharedTestUser: any = null;
// متغير لحفظ كلمة المرور الخام للمستخدم المشترك
const sharedTestUserPassword: string = 'Test@123';

/**
 * الحصول على كلمة المرور الخام للمستخدم المشترك
 */
export const getSharedTestUserPassword = (): string => {
  return sharedTestUserPassword;
};

/**
 * إنشاء مستخدم اختبار مشترك
 */
export const createIntegrationTestUser = async () => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  // إذا كان المستخدم موجود بالفعل، تحقق من وجوده في قاعدة البيانات
  if (sharedTestUser) {
    try {
      const existingUser = await testPrisma.user.findUnique({
        where: { id: sharedTestUser.id }
      });
      if (existingUser) {
        // التأكد من أن كلمة المرور صحيحة
        const isPasswordValid = await bcrypt.compare(sharedTestUserPassword, existingUser.password);
        if (isPasswordValid) {
          console.log('🔄 استخدام مستخدم الاختبار الموجود:', {
            id: existingUser.id,
            username: existingUser.username
          });
          return existingUser;
        } else {
          // إعادة تعيين كلمة المرور إذا لم تكن صحيحة
          const hashedPassword = await bcrypt.hash(sharedTestUserPassword, 12);
          const updatedUser = await testPrisma.user.update({
            where: { id: existingUser.id },
            data: { password: hashedPassword, isActive: true }
          });
          console.log('🔄 تم إعادة تعيين كلمة المرور للمستخدم الموجود');
          return updatedUser;
        }
      }
    } catch (error) {
      console.warn('تحذير: فشل في التحقق من المستخدم الموجود:', error);
    }
  }

  try {
    const timestamp = Date.now();
    const username = `test_admin_${timestamp}`;

    // التأكد من تشفير كلمة المرور بشكل صحيح
    const password = sharedTestUserPassword;
    if (!password) {
      throw new Error('كلمة المرور مطلوبة');
    }

    // تنظيف أي بيانات قديمة للمستخدم
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.loginAttempt.deleteMany({});

    const hashedPassword = await bcrypt.hash(password, 12);

    // التحقق من أن كلمة المرور تم تشفيرها
    if (!hashedPassword) {
      throw new Error('فشل في تشفير كلمة المرور');
    }

    // التحقق من صحة التشفير
    const isHashValid = await bcrypt.compare(password, hashedPassword);
    if (!isHashValid) {
      throw new Error('فشل في التحقق من تشفير كلمة المرور');
    }

    const user = await testPrisma.user.create({
      data: {
        id: `test-user-${timestamp}`,
        username,
        password: hashedPassword,
        name: 'مستخدم اختبار',
        email: `test_${timestamp}@example.com`,
        role: 'ADMIN',
        isActive: true,
      },
    });

    // التحقق من إنشاء المستخدم بنجاح
    if (!user || !user.password) {
      throw new Error('فشل في إنشاء المستخدم أو كلمة المرور فارغة');
    }

    // التحقق النهائي من كلمة المرور
    const finalPasswordCheck = await bcrypt.compare(password, user.password);
    if (!finalPasswordCheck) {
      throw new Error('فشل في التحقق النهائي من كلمة المرور');
    }

    // حفظ المستخدم للاستخدام المشترك
    sharedTestUser = user;

    console.log('✅ تم إنشاء مستخدم الاختبار الجديد:', {
      id: user.id,
      username: user.username,
      hasPassword: !!user.password,
      passwordLength: user.password.length,
      passwordVerified: finalPasswordCheck
    });

    return user;
  } catch (error) {
    console.error('❌ خطأ في إنشاء مستخدم الاختبار:', error);
    throw error;
  }
};

/**
 * الحصول على رمز المصادقة للاختبارات
 */
export const getIntegrationAuthToken = async (user: any): Promise<string> => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  try {
    // تنظيف البيانات المتعلقة بالمستخدم
    await testPrisma.invalidatedToken.deleteMany({
      where: { userId: user.id }
    });

    await testPrisma.loginAttempt.deleteMany({
      where: { username: user.username }
    });

    // التأكد من أن المستخدم نشط
    await testPrisma.user.update({
      where: { id: user.id },
      data: { isActive: true }
    });

    // إنشاء JWT (متوافق مع middleware المصادقة)
    const payload = {
      id: user.id,
      username: user.username,
      role: user.role
    };

    const token = generateToken(payload, 'access', '24h');
    console.log('✅ تم إنشاء رمز المصادقة للاختبار');

    return token;
  } catch (error) {
    console.error('❌ خطأ في الحصول على رمز المصادقة:', error);
    throw error;
  }
};

/**
 * إنشاء بيانات تجريبية أساسية
 */
export const seedIntegrationTestData = async () => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  try {
    console.log('🌱 إضافة بيانات تجريبية...');

    // إنشاء مستخدم
    const user = await createIntegrationTestUser();

    // إنشاء عميل تجريبي
    const timestamp = Date.now();
    const client = await testPrisma.client.create({
      data: {
        name: 'شركة الاختبار',
        taxNumber: `TX${timestamp}`,
        phone: '+966501234567',
        email: `client_${timestamp}@test.com`,
        address: 'الرياض، المملكة العربية السعودية'
      }
    });

    // إنشاء بيان تجريبي
    const declaration = await testPrisma.declaration.create({
      data: {
        declarationNumber: `D${timestamp}`,
        taxNumber: client.taxNumber,
        clientName: client.name,
        companyName: client.name,
        policyNumber: '12345',
        invoiceNumber: '67890',
        gatewayEntryNumber: '11111',
        declarationType: 'IMPORT',
        declarationDate: new Date(),
        count: 10,
        weight: 100.5,
        goodsType: 'MEDICAL_SUPPLIES',
        itemsCount: 5,
        clientId: client.id,
        userId: user.id
      }
    });

    // إنشاء حركة صنف تجريبية
    await testPrisma.itemMovement.create({
      data: {
        declarationId: declaration.id,
        itemName: 'أدوية طبية',
        quantity: 100,
        unit: 'قطعة',
        movementDate: new Date(),
        movementType: 'استلام',
        notes: 'حركة اختبارية'
      }
    });

    console.log('✅ تم إضافة البيانات التجريبية بنجاح');

    return { user, client, declaration };
  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
    throw error;
  }
};

/**
 * إغلاق الاتصال بقاعدة البيانات
 */
export const disconnectIntegrationDatabase = async (): Promise<void> => {
  try {
    // استخدام الدالة المحسنة من test-database-setup
    await disconnectTestDatabase();

    // انتظار إضافي للتأكد من إغلاق الاتصال
    await new Promise(resolve => setTimeout(resolve, 100));
  } catch (error) {
    console.warn('تحذير: خطأ في قطع الاتصال:', error instanceof Error ? error.message : String(error));
  }
};

/**
 * الحصول على عميل Prisma للاختبارات
 */
export const getIntegrationPrismaClient = () => {
  return testPrisma;
};

// إعداد Jest hooks
beforeAll(async () => {
  await setupIntegrationDatabase();
  await cleanupIntegrationDatabase();
}, 180000); // 3 دقائق timeout

afterAll(async () => {
  await cleanupIntegrationDatabase();
  await disconnectIntegrationDatabase();
}, 60000); // دقيقة واحدة timeout

beforeEach(async () => {
  // إعادة تعيين البيانات قبل كل اختبار مع الحفاظ على المستخدم المشترك
  // لا نحذف المستخدم في beforeEach لتجنب مشاكل اختبارات المصادقة
  try {
    // حذف البيانات الأخرى فقط - إزالة الجداول غير الموجودة
    await testPrisma.itemMovement.deleteMany({});
    await testPrisma.receipt.deleteMany({});
    await testPrisma.permit.deleteMany({});
    await testPrisma.release.deleteMany({});
    await testPrisma.authorization.deleteMany({});
    await testPrisma.driver.deleteMany({});
    await testPrisma.declaration.deleteMany({});
    await testPrisma.client.deleteMany({});
    await testPrisma.loginAttempt.deleteMany({});
    await testPrisma.session.deleteMany({});
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.customForm.deleteMany({});
    await testPrisma.reportTemplate.deleteMany({});
    await testPrisma.auditLog.deleteMany({});
    // لا نحذف المستخدمين هنا
  } catch (error) {
    console.warn('تحذير: خطأ في تنظيف البيانات:', error);
  }
});
