// Prisma Client للاختبارات - يستخدم SQLite
import { PrismaClient } from '@prisma/client';

// تعيين DATABASE_URL للاختبارات قبل إنشاء Prisma Client
if (process.env.NODE_ENV === 'test') {
  process.env.DATABASE_URL = 'file:./test.db';
}

// إنشاء instance من Prisma Client للاختبارات
export const testPrisma = new PrismaClient({
  log: process.env.NODE_ENV === 'test' ? [] : ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: 'file:./test.db'
    }
  }
});

// دالة لإعداد قاعدة البيانات للاختبارات
export const setupTestDatabase = async () => {
  try {
    console.log('🔌 الاتصال بقاعدة بيانات الاختبار...');
    await testPrisma.$connect();
    console.log('✅ تم الاتصال بقاعدة بيانات الاختبار بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة بيانات الاختبار:', error);
    throw error;
  }
};

// دالة لتنظيف قاعدة البيانات
export const cleanupTestDatabase = async () => {
  try {
    console.log('🧹 تنظيف قاعدة بيانات الاختبار...');

    // حذف البيانات بترتيب يحترم القيود الخارجية (مع معالجة الأخطاء)
    try {
      await testPrisma.itemMovement.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف ItemMovement');
    }

    try {
      await testPrisma.receipt.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Receipt');
    }

    try {
      await testPrisma.guarantee.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Guarantee');
    }

    try {
      await testPrisma.permit.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Permit');
    }

    try {
      await testPrisma.release.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Release');
    }

    try {
      await testPrisma.authorization.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Authorization');
    }

    try {
      await testPrisma.driver.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Driver');
    }

    try {
      await testPrisma.declaration.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Declaration');
    }

    try {
      await testPrisma.client.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Client');
    }

    try {
      await testPrisma.customForm.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف CustomForm');
    }

    try {
      await testPrisma.reportTemplate.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف ReportTemplate');
    }

    try {
      await testPrisma.document.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Document');
    }

    try {
      await testPrisma.session.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف Session');
    }

    try {
      await testPrisma.loginAttempt.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف LoginAttempt');
    }

    try {
      await testPrisma.invalidatedToken.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف InvalidatedToken');
    }

    try {
      await testPrisma.auditLog.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف AuditLog');
    }

    try {
      await testPrisma.user.deleteMany({});
    } catch (error) {
      console.warn('⚠️ تحذير: لا يمكن حذف User');
    }

    console.log('✅ تم تنظيف قاعدة بيانات الاختبار');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ في تنظيف قاعدة البيانات:', error);
  }
};

// دالة لقطع الاتصال
export const disconnectTestDatabase = async () => {
  try {
    await testPrisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة بيانات الاختبار');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ في قطع الاتصال:', error);
  }
};

// دالة لإنشاء مستخدم تجريبي
export const createTestUser = async (overrides: any = {}) => {
  const timestamp = Date.now();
  const username = overrides.username || `test_admin_${timestamp}`;

  console.log(`👤 إنشاء مستخدم تجريبي: ${username}`);

  try {
    const user = await testPrisma.user.create({
      data: {
        id: `test-user-${timestamp}`,
        username,
        email: overrides.email || `${username}@test.com`,
        password: overrides.password || '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // Test@123
        name: overrides.name || 'مستخدم تجريبي',
        role: overrides.role || 'ADMIN',
        isActive: true,
        ...overrides
      }
    });

    console.log(`✅ تم إنشاء المستخدم التجريبي: ${user.username} (${user.id})`);
    return user;
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم التجريبي:', error);
    throw error;
  }
};

export default testPrisma;
