import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

// تعريف نوع البيان
type DeclarationType = 'IMPORT' | 'EXPORT';

// تعريف نوع البضاعة
type GoodsType = 'HUMAN_MEDICINE' | 'LABORATORY_SOLUTIONS' | 'MEDICAL_SUPPLIES' | 'SUGAR_STRIPS' | 'MEDICAL_DEVICES' | 'MISCELLANEOUS';

// واجهة البيان
interface Declaration {
  id: string;
  declarationNumber: string;
  taxNumber: string;
  clientName: string;
  companyName?: string | null;
  policyNumber?: string | null;
  invoiceNumber?: string | null;
  gatewayEntryNumber?: string | null;
  declarationType: DeclarationType;
  declarationDate: Date;
  count?: number | null;
  weight?: number | null;
  goodsType?: GoodsType | null;
  itemsCount?: number | null;
  entryDate?: Date | null;
  exitDate?: Date | null;
  pdfFile?: string | null;
  clientId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  drivers?: Driver[];
  client?: Client | null;
}

// واجهة السائق
interface Driver {
  id: string;
  declarationId: string;
  driverName: string;
  truckNumber: string;
  trailerNumber?: string | null;
  driverPhone?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة العميل
interface Client {
  id: string;
  name: string;
  taxNumber: string;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة إنشاء البيان
interface CreateDeclarationInput {
  declarationNumber?: string;
  taxNumber: string;
  clientName: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType: DeclarationType;
  declarationDate: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string | null;
  drivers?: {
    driverName: string;
    truckNumber: string;
    trailerNumber?: string;
    driverPhone?: string;
  }[];
}

// واجهة تحديث البيان
interface UpdateDeclarationInput {
  taxNumber?: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType?: DeclarationType;
  declarationDate?: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string | null;
  drivers?: {
    id?: string;
    driverName: string;
    truckNumber: string;
    trailerNumber?: string;
    driverPhone?: string;
  }[];
}

// واجهة معلمات قائمة البيانات
interface ListDeclarationsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationType?: DeclarationType;
  fromDate?: Date;
  toDate?: Date;
  clientId?: string;
}

// خدمة البيانات
export const declarationService = {
  // إنشاء بيان جديد
  createDeclaration: async (
    data: CreateDeclarationInput,
    userId: string,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من رقم البيان إذا تم تقديمه
      let declarationNumber: string;

      if (data.declarationNumber) {
        // التحقق من عدم تكرار رقم البيان
        const existingDeclaration = await prisma.declaration.findFirst({
          where: { declarationNumber: data.declarationNumber },
        });

        if (existingDeclaration) {
          throw new HttpException(400, 'رقم البيان موجود مسبقاً', 'Duplicate Declaration Number');
        }

        declarationNumber = data.declarationNumber;
      } else {
        // إنشاء رقم بيان جديد تلقائياً
        const lastDeclaration = await prisma.declaration.findFirst({
          orderBy: { declarationNumber: 'desc' },
          select: { declarationNumber: true }
        });

        declarationNumber = lastDeclaration
          ? `${parseInt(lastDeclaration.declarationNumber) + 1}`
          : '1001';
      }

      // إنشاء البيان باستخدام Prisma ORM مع تحويل أنواع البيانات
      const declaration = await prisma.declaration.create({
        data: {
          declarationNumber,
          taxNumber: data.taxNumber,
          clientName: data.clientName,
          companyName: data.companyName,
          policyNumber: data.policyNumber,
          invoiceNumber: data.invoiceNumber,
          gatewayEntryNumber: data.gatewayEntryNumber || '',
          declarationType: data.declarationType,
          declarationDate: data.declarationDate,
          count: data.count ? parseInt(data.count.toString()) : null,
          weight: data.weight ? parseFloat(data.weight.toString()) : null,
          goodsType: data.goodsType,
          itemsCount: data.itemsCount ? parseInt(data.itemsCount.toString()) : null,
          entryDate: data.entryDate,
          exitDate: data.exitDate,
          clientId: data.clientId,
        },
      });

      const declarationId = declaration.id;

      // إنشاء السائقين إذا تم تقديمهم
      if (data.drivers && data.drivers.length > 0) {
        try {
          await prisma.driver.createMany({
            data: data.drivers.map(driver => ({
              driverName: driver.driverName,
              truckNumber: driver.truckNumber,
              trailerNumber: driver.trailerNumber || null,
              driverPhone: driver.driverPhone || null,
              declarationId: declarationId,
            })),
          });
        } catch (error) {
          console.warn('تحذير: خطأ في إنشاء السائقين:', error);
          // لا نرمي خطأ، سنستمر بدون السائقين
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      if (file) {
        const pdfPath = saveUploadedPdf(file, 'declarations', declarationId);
        await prisma.declaration.update({
          where: { id: declarationId },
          data: { pdfFile: pdfPath },
        });
      }

      // استرجاع البيان مع العلاقات باستخدام Prisma ORM العادي
      const result = await prisma.declaration.findUnique({
        where: { id: declarationId },
        include: {
          drivers: true,
          client: true,
        },
      });

      return result;
    } catch (error: any) {
      console.error('خطأ في إنشاء البيان:', error);

      // إذا كان الخطأ من نوع HttpException، أعد رميه كما هو
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(500, 'حدث خطأ أثناء إنشاء البيان', 'Declaration Creation Error');
    }
  },

  // تحديث بيان
  updateDeclaration: async (
    id: string,
    data: UpdateDeclarationInput,
    _userId: string, // استخدام _ للإشارة إلى أن المتغير غير مستخدم
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const existingDeclaration = await prisma.declaration.findUnique({
        where: { id },
        select: { id: true, pdfFile: true },
      });

      if (!existingDeclaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
      }

      let pdfFilePath = existingDeclaration.pdfFile;
      if (file) {
        pdfFilePath = saveUploadedPdf(file, 'declarations', id);
      }

      // استخدام Prisma ORM العادي بدلاً من raw queries للتوافق مع SQLite
      const updateData: any = {};

      // declarationNumber is auto-generated, no need to update
      if (data.declarationType !== undefined) updateData.declarationType = data.declarationType;
      if (data.declarationDate !== undefined) updateData.declarationDate = data.declarationDate;
      if (data.clientName !== undefined) updateData.clientName = data.clientName;
      if (data.companyName !== undefined) updateData.companyName = data.companyName;
      if (data.goodsType !== undefined) updateData.goodsType = data.goodsType;
      if (data.count !== undefined) updateData.count = data.count ? parseInt(data.count.toString()) : null;
      if (data.weight !== undefined) updateData.weight = data.weight ? parseFloat(data.weight.toString()) : null;
      if (data.itemsCount !== undefined) updateData.itemsCount = data.itemsCount ? parseInt(data.itemsCount.toString()) : null;
      // goodsValue and customsValue are not in the current schema
      if (data.policyNumber !== undefined) updateData.policyNumber = data.policyNumber;
      if (data.invoiceNumber !== undefined) updateData.invoiceNumber = data.invoiceNumber;
      if (data.gatewayEntryNumber !== undefined) updateData.gatewayEntryNumber = data.gatewayEntryNumber;
      if (data.itemsCount !== undefined) updateData.itemsCount = data.itemsCount;
      if (data.entryDate !== undefined) updateData.entryDate = data.entryDate;
      if (data.exitDate !== undefined) updateData.exitDate = data.exitDate;
      if (data.clientId !== undefined) updateData.clientId = data.clientId;
      if (pdfFilePath !== undefined) updateData.pdfFile = pdfFilePath;
      updateData.updatedAt = new Date();

      // تحديث البيان
      await prisma.declaration.update({
        where: { id },
        data: updateData,
      });

      // تحديث السائقين إذا تم تقديمهم
      if (data.drivers && data.drivers.length > 0) {
        // حذف السائقين الحاليين
        await prisma.driver.deleteMany({
          where: { declarationId: id },
        });

        // إنشاء سائقين جدد
        await prisma.driver.createMany({
          data: data.drivers.map(driver => ({
            driverName: driver.driverName,
            truckNumber: driver.truckNumber,
            trailerNumber: driver.trailerNumber || null,
            driverPhone: driver.driverPhone || null,
            declarationId: id,
          })),
        });
      }

      // استرجاع البيان المحدث مع العلاقات
      const updatedDeclaration = await prisma.declaration.findUnique({
        where: { id },
        include: {
          drivers: true,
          client: true,
        },
      });

      return updatedDeclaration;
    } catch (error: any) {
      console.error('خطأ في تحديث البيان:', error);
      throw new HttpException(500, 'حدث خطأ أثناء تحديث البيان', 'Declaration Update Error');
    }
  },

  // الحصول على بيان بواسطة المعرف
  getDeclaration: async (id: string) => {
    // استخدام Prisma ORM العادي للتوافق مع SQLite
    const declaration = await prisma.declaration.findUnique({
      where: { id },
      include: {
        drivers: true,
        client: true,
      },
    });

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
    }

    return declaration;
  },

  // حذف بيان
  deleteDeclaration: async (id: string, _userId: string) => { // استخدام _ للإشارة إلى أن المتغير غير مستخدم
    // التحقق من وجود البيان
    const existingDeclaration = await prisma.declaration.findUnique({
      where: { id },
    });

    if (!existingDeclaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
    }

    // حذف السائقين المرتبطين بالبيان
    await prisma.driver.deleteMany({
      where: { declarationId: id },
    });

    // حذف البيان
    await prisma.declaration.delete({
      where: { id },
    });

    return { success: true, message: 'تم حذف البيان بنجاح' };
  },

  // الحصول على قائمة البيانات
  listDeclarations: async (params: ListDeclarationsParams) => {
    const {
      page = 1,
      limit = 10,
      sort = 'declarationNumber',
      order = 'desc',
      // استخدام البحث إذا تم تقديمه (غير مستخدم حاليًا)
      search: _search = '',
      declarationType,
      fromDate,
      toDate,
      clientId,
    } = params;

    // استخدام Prisma ORM العادي بدلاً من raw queries للتوافق مع SQLite
    const whereCondition: any = {};

    if (declarationType) {
      whereCondition.declarationType = declarationType;
    }

    if (clientId) {
      whereCondition.clientId = clientId;
    }

    if (fromDate && toDate) {
      whereCondition.declarationDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      whereCondition.declarationDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      whereCondition.declarationDate = {
        lte: toDate,
      };
    }

    // الحصول على البيانات باستخدام Prisma ORM
    const declarations = await prisma.declaration.findMany({
      where: whereCondition,
      include: {
        drivers: true,
        client: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // الحصول على إجمالي عدد البيانات
    const totalCount = await prisma.declaration.count({
      where: whereCondition,
    });

    return {
      data: declarations,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },
};
