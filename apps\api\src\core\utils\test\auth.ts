import { testPrisma } from './test-prisma-client.js';
import bcrypt from 'bcryptjs';
import { generateToken } from '../jwt.js';

/**
 * إنشاء مستخدم اختبار محسن
 */
export const createTestUser = async () => {
  try {
    // إنشاء اسم مستخدم فريد
    const timestamp = Date.now();
    const username = `test_admin_${timestamp}`;

    // تنظيف شامل للبيانات المرتبطة (مع معالجة الأخطاء)
    try {
      await testPrisma.invalidatedToken.deleteMany({});
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف InvalidatedToken (الجدول قد لا يكون موجود)');
    }

    try {
      await testPrisma.loginAttempt.deleteMany({});
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف LoginAttempt (الجدول قد لا يكون موجود)');
    }

    try {
      await testPrisma.user.deleteMany({});
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف User (الجدول قد لا يكون موجود)');
    }

    // إنشاء كلمة مرور مشفرة
    const hashedPassword = await bcrypt.hash('Test@123', 12);

    // إنشاء مستخدم مع التأكد من أنه نشط
    const user = await testPrisma.user.create({
      data: {
        id: `test-user-${timestamp}`,
        username,
        password: hashedPassword,
        name: 'مستخدم اختبار',
        email: `test_${timestamp}@example.com`,
        role: 'ADMIN',
        isActive: true, // التأكد من أن المستخدم نشط
      },
    });

    // التحقق المضاعف من حالة المستخدم
    const verifiedUser = await testPrisma.user.findUnique({
      where: { id: user.id },
      select: { id: true, username: true, password: true, isActive: true, role: true }
    });

    console.log('🔍 تفاصيل المستخدم المتحقق منه:', {
      found: !!verifiedUser,
      id: verifiedUser?.id,
      username: verifiedUser?.username,
      isActive: verifiedUser?.isActive,
      role: verifiedUser?.role
    });

    if (!verifiedUser) {
      throw new Error('فشل في العثور على المستخدم بعد الإنشاء');
    }

    // إذا لم يكن المستخدم نشطاً، قم بتفعيله
    if (!verifiedUser.isActive) {
      console.log('⚠️ المستخدم غير نشط، جاري التفعيل...');
      const activatedUser = await testPrisma.user.update({
        where: { id: user.id },
        data: { isActive: true },
        select: { id: true, username: true, password: true, isActive: true, role: true }
      });

      console.log('✅ تم تفعيل المستخدم:', {
        id: activatedUser.id,
        isActive: activatedUser.isActive
      });

      return activatedUser;
    }

    console.log('✅ تم إنشاء مستخدم اختبار نشط:', {
      id: verifiedUser.id,
      username: verifiedUser.username,
      isActive: verifiedUser.isActive,
      role: verifiedUser.role,
      hasPassword: !!verifiedUser.password
    });

    return verifiedUser;
  } catch (error) {
    console.error('❌ خطأ في إنشاء مستخدم الاختبار:', error);
    throw error;
  }
};

/**
 * الحصول على رمز المصادقة محسن
 */
export const getAuthToken = async (user: any) => {
  try {
    // التأكد من أن المستخدم نشط
    if (!user || !user.id) {
      throw new Error('مستخدم غير صالح للحصول على رمز المصادقة');
    }

    // التحقق من حالة المستخدم في قاعدة البيانات
    const currentUser = await testPrisma.user.findUnique({
      where: { id: user.id },
      select: { id: true, username: true, role: true, isActive: true }
    });

    if (!currentUser) {
      throw new Error('المستخدم غير موجود في قاعدة البيانات');
    }

    if (!currentUser.isActive) {
      // تفعيل المستخدم إذا لم يكن نشطاً
      await testPrisma.user.update({
        where: { id: user.id },
        data: { isActive: true }
      });
    }

    // إنشاء payload للتوكن (متوافق مع middleware المصادقة)
    const payload = {
      id: currentUser.id,
      username: currentUser.username,
      role: currentUser.role
    };

    // إنشاء JWT مع مدة صلاحية طويلة للاختبارات
    const token = generateToken(payload, 'access', '24h');

    console.log('✅ تم إنشاء رمز مصادقة للاختبار:', {
      userId: currentUser.id,
      username: currentUser.username,
      tokenLength: token.length
    });

    return token;
  } catch (error) {
    console.error('❌ خطأ في الحصول على رمز المصادقة:', error);
    throw error;
  }
};

/**
 * تنظيف بيانات الاختبار
 */
export const cleanupTestData = async () => {
  try {
    // حذف جميع التوكنات المبطلة (مع معالجة الأخطاء)
    try {
      await testPrisma.invalidatedToken.deleteMany({});
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف InvalidatedToken');
    }

    // حذف جميع محاولات تسجيل الدخول (مع معالجة الأخطاء)
    try {
      await testPrisma.loginAttempt.deleteMany({});
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف LoginAttempt');
    }

    // حذف المستخدمين الاختباريين (مع معالجة الأخطاء)
    try {
      await testPrisma.user.deleteMany({
        where: {
          username: {
            startsWith: 'test_user_'
          }
        }
      });
    } catch (error) {
      console.log('⚠️ تحذير: لا يمكن حذف المستخدمين الاختباريين');
    }

    console.log('تم تنظيف بيانات الاختبار');
  } catch (error) {
    console.error('خطأ في تنظيف بيانات الاختبار:', error);
  }
};
