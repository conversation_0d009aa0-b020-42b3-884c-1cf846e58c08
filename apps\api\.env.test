# بيئة الاختبار
NODE_ENV=test

# قاعدة البيانات للاختبار (SQLite للاختبارات السريعة)
DATABASE_URL="file:./test.db"

# إعدادات JWT
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_REFRESH_SECRET=test_jwt_refresh_secret_key_for_testing_only
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# إعدادات الخادم
PORT=3001
HOST=localhost

# إعدادات التحميل
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# إعدادات الأمان
BCRYPT_ROUNDS=10

# تعطيل نظام القفل في الاختبارات
DISABLE_USER_LOCKING=true

# إعدادات أخرى
LOG_LEVEL=error
