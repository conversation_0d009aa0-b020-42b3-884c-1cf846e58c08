#!/usr/bin/env node

/**
 * Script لتبديل schema.prisma للاختبارات
 * يقوم بتغيير provider من postgresql إلى sqlite مؤقتاً للاختبارات
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const schemaPath = path.join(__dirname, '../../../database/schema.prisma');
const backupPath = path.join(__dirname, '../../../database/schema.prisma.backup');

async function setupTestSchema() {
  try {
    // قراءة schema الحالي
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');

    // إنشاء backup
    fs.writeFileSync(backupPath, schemaContent);
    console.log('✅ تم إنشاء backup من schema.prisma');

    // تعديل schema للاختبارات
    let testSchemaContent = schemaContent
      .replace('provider = "postgresql"', 'provider = "sqlite"')
      .replace(/url\s*=\s*env\("DATABASE_URL"\)/g, 'url = "file:./test.db"')
      .replace(/url\s*=\s*"file:\.\/test\.db"/g, 'url = "file:./test.db"');

    // كتابة schema المعدل
    fs.writeFileSync(schemaPath, testSchemaContent);
    console.log('✅ تم تعديل schema.prisma للاختبارات (SQLite)');

    // تطبيق migrations على قاعدة البيانات الجديدة
    console.log('🔧 تطبيق migrations على قاعدة البيانات...');
    const { execSync } = await import('child_process');

    try {
      // حذف قاعدة البيانات القديمة إن وجدت
      const testDbPath = path.join(__dirname, '../../../test.db');
      if (fs.existsSync(testDbPath)) {
        fs.unlinkSync(testDbPath);
        console.log('🗑️ تم حذف قاعدة البيانات القديمة');
      }

      // تطبيق migrations
      execSync('npx prisma db push --force-reset', {
        cwd: path.join(__dirname, '../../..'),
        stdio: 'inherit',
        env: { ...process.env, PRISMA_SCHEMA_PATH: schemaPath }
      });
      console.log('✅ تم تطبيق migrations بنجاح');
    } catch (migrationError) {
      console.error('❌ خطأ في تطبيق migrations:', migrationError.message);
      // لا نوقف العملية، سنحاول المتابعة
    }

  } catch (error) {
    console.error('❌ خطأ في إعداد schema للاختبارات:', error);
    process.exit(1);
  }
}

function restoreSchema() {
  try {
    if (fs.existsSync(backupPath)) {
      // استعادة schema الأصلي
      const originalContent = fs.readFileSync(backupPath, 'utf8');
      fs.writeFileSync(schemaPath, originalContent);

      // حذف backup
      fs.unlinkSync(backupPath);
      console.log('✅ تم استعادة schema.prisma الأصلي (PostgreSQL)');
    }
  } catch (error) {
    console.error('❌ خطأ في استعادة schema:', error);
    process.exit(1);
  }
}

// تحديد العملية بناءً على المعامل
const operation = process.argv[2];

(async () => {
  if (operation === 'setup') {
    await setupTestSchema();
  } else if (operation === 'restore') {
    restoreSchema();
  } else {
    console.log('الاستخدام:');
    console.log('  node setup-test-schema.js setup   - إعداد schema للاختبارات');
    console.log('  node setup-test-schema.js restore - استعادة schema الأصلي');
    process.exit(1);
  }
})();
