// إعداد محسن لقاعدة البيانات للاختبارات
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import path from 'path';

// تعيين بيئة الاختبار أولاً
process.env.NODE_ENV = 'test';

// تعيين DATABASE_URL للاختبارات قبل أي شيء آخر
process.env.DATABASE_URL = 'file:./test.db';

// تحميل متغيرات البيئة للاختبار
dotenv.config({
  path: path.join(process.cwd(), '.env.test'),
  override: true
});

// إنشاء Prisma Client محسن للاختبارات
export const createTestPrismaClient = () => {
  return new PrismaClient({
    log: [],
    datasources: {
      db: {
        url: 'file:./test.db'
      }
    }
  });
};

// عميل Prisma للاختبارات
export const testPrisma = createTestPrismaClient();

/**
 * إعداد قاعدة البيانات للاختبارات
 */
export const setupTestDatabase = async () => {
  try {
    console.log('📋 إعداد قاعدة البيانات للاختبارات...');
    
    // التأكد من DATABASE_URL
    process.env.DATABASE_URL = 'file:./test.db';
    
    // الاتصال بقاعدة البيانات
    await testPrisma.$connect();
    
    console.log('✅ تم إعداد قاعدة البيانات للاختبارات بنجاح');
  } catch (error) {
    console.warn('⚠️ تحذير في إعداد قاعدة البيانات:', error instanceof Error ? error.message : String(error));
    // لا نرمي خطأ، سنستمر مع mock
  }
};

/**
 * تنظيف قاعدة البيانات
 */
export const cleanupTestDatabase = async (preserveUsers = false) => {
  try {
    console.log('🧹 تنظيف قاعدة البيانات...');

    // حذف البيانات بترتيب يحترم القيود الخارجية
    const cleanupOperations = [
      () => testPrisma.itemMovement.deleteMany().catch(() => {}),
      () => testPrisma.authorization.deleteMany().catch(() => {}),
      () => testPrisma.release.deleteMany().catch(() => {}),
      () => testPrisma.permit.deleteMany().catch(() => {}),
      () => testPrisma.receipt.deleteMany().catch(() => {}),
      () => testPrisma.driver.deleteMany().catch(() => {}),
      () => testPrisma.declaration.deleteMany().catch(() => {}),
      () => testPrisma.client.deleteMany().catch(() => {}),
      () => testPrisma.session.deleteMany().catch(() => {}),
      () => testPrisma.customForm.deleteMany().catch(() => {}),
      () => testPrisma.reportTemplate.deleteMany().catch(() => {}),
      () => testPrisma.loginAttempt.deleteMany().catch(() => {}),
      () => testPrisma.invalidatedToken.deleteMany().catch(() => {}),
      () => testPrisma.auditLog.deleteMany().catch(() => {})
    ];

    // تنفيذ عمليات التنظيف
    for (const operation of cleanupOperations) {
      try {
        await operation();
      } catch (error) {
        // تجاهل الأخطاء في التنظيف
      }
    }

    // حذف المستخدمين إذا لم يكن مطلوب الحفاظ عليهم
    if (!preserveUsers) {
      try {
        await testPrisma.user.deleteMany();
      } catch (error) {
        // تجاهل الأخطاء
      }
    }

    console.log('✅ تم تنظيف قاعدة البيانات');
  } catch (error) {
    console.warn('⚠️ تحذير في تنظيف قاعدة البيانات:', error);
  }
};

/**
 * قطع الاتصال بقاعدة البيانات
 */
export const disconnectTestDatabase = async () => {
  try {
    await testPrisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  } catch (error) {
    console.warn('⚠️ تحذير في قطع الاتصال:', error);
  }
};

/**
 * إعادة تعيين قاعدة البيانات بالكامل
 */
export const resetTestDatabase = async () => {
  try {
    await cleanupTestDatabase(false);
    await setupTestDatabase();
  } catch (error) {
    console.warn('⚠️ تحذير في إعادة تعيين قاعدة البيانات:', error);
  }
};

export default testPrisma;
